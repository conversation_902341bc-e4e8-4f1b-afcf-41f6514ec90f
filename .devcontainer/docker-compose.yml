services:
  sentry-build: &sentry-build
    image: ${DOCKER_IMAGE:-sentry-ruby-devcontainer}:${DOCKER_TAG:-latest}
    build:
      context: ..
      dockerfile: .devcontainer/Dockerfile
      args:
        IMAGE: ${IMAGE}
    volumes:
      - ..:/workspace/sentry:cached
    working_dir: /workspace/sentry
    env_file: [".env"]

  sentry-dev:
    <<: *sentry-build
    entrypoint: ".devcontainer/run --service dev"
    command: "sleep infinity"
    depends_on:
      - redis

  sentry-test:
    <<: *sentry-build
    entrypoint: ".devcontainer/run --service test"
    depends_on:
      - sentry-test-services

  sentry-test-services:
    <<: *sentry-build
    entrypoint: ".devcontainer/run --service test-services"
    command: "foreman start"
    ports:
      - "${SENTRY_E2E_RAILS_APP_PORT}:4000"
      - "${SENTRY_E2E_SVELTE_APP_PORT}:4001"

  redis:
    image: redis:latest
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
    ports:
      - "6379:6379"
