name: "Build & push devcontainer"

on:
  push:
    branches:
      - master
    paths:
      - .devcontainer/**
      - .github/workflows/build_images.yml

  # Uncomment if you want to test things out in a PR
  #
  # pull_request:
  #   paths:
  #     - .devcontainer/**
  #     - .github/workflows/build_images.yml

permissions:
  contents: read
  packages: write # Required for GHCR

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        ruby_image:
          - ruby:2.7.8-slim-bullseye
          - ruby:3.0.7-slim-bullseye
          - ruby:3.1.7-slim-bookworm
          - ruby:3.2.9-slim-bookworm
          - ruby:3.3.9-slim-bookworm
          - ruby:3.4.5-slim-bookworm

    steps:
      - name: Check out current commit
        uses: actions/checkout@v4

      - name: Generate short image name
        id: image_name
        run: |
          ruby_image="${{ matrix.ruby_image }}"

          if [[ "$ruby_image" == ruby:* ]]; then
            version=$(echo "$ruby_image" | cut -d: -f2 | cut -d. -f1,2)
            short_name="sentry-ruby-devcontainer-${version}"
          elif [[ "$ruby_image" == jruby:latest ]]; then
            short_name="sentry-ruby-devcontainer-jruby-latest"
          elif [[ "$ruby_image" == jruby:* ]]; then
            version=$(echo "$ruby_image" | cut -d: -f2 | cut -d. -f1,2)
            short_name="sentry-ruby-devcontainer-jruby-${version}"
          else
            short_name="sentry-ruby-devcontainer-${ruby_image}"
          fi

          echo "short_name=${short_name}" >> $GITHUB_OUTPUT
          echo "Generated short image name: ${short_name}"

      - name: Build and push devcontainer image
        id: build
        uses: getsentry/action-build-and-push-images@main
        with:
          image_name: '${{ steps.image_name.outputs.short_name }}'
          dockerfile_path: '.devcontainer/Dockerfile'
          ghcr: true
          publish_on_pr: true
          build_args: |
            IMAGE=${{ matrix.ruby_image }}

      - name: Use outputs
        run: |
          echo "GHCR URL: ${{ steps.build.outputs.ghcr_image_url }}"
